package com.mitdd.gazetracker.movement.vm

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.google.gson.Gson
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.bean.SaccadeAbilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.movement.bean.FileUploadData
import com.mitdd.gazetracker.movement.repository.SaccadeAbilityRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.*

/**
 * FileName: SaccadeAbilityViewModel
 * Author by lilin,Date on 2025/6/21 10:30
 * PS: Not easy to write code, please indicate.
 * 扫视能力检测ViewModel
 */
class SaccadeAbilityViewModel : ViewModel() {

    companion object {
        private val TAG = SaccadeAbilityViewModel::class.java.simpleName
    }

    private val saccadeAbilityRepository = SaccadeAbilityRepository()
    private val gson = Gson()

    /**
     * 提交结果LiveData
     */
    val submitResultLiveData = MutableLiveData<SaccadeAbilityAdd?>()

    /**
     * 图片上传结果LiveData
     */
    val uploadImageResultLiveData = MutableLiveData<FileUploadResponse?>()

    /**
     * 上传图片
     * @param imageFile 图片文件
     */
    fun uploadImage(imageFile: File) {
        Logger.d(TAG, msg = "开始上传扫视能力检测结果图片: ${imageFile.absolutePath}")
        
        viewModelScope.launch {
            try {
                val requestFile = imageFile.asRequestBody("image/*".toMediaTypeOrNull())
                val body = MultipartBody.Part.createFormData("file", imageFile.name, requestFile)
                
                MutableStateFlow(saccadeAbilityRepository.uploadImage(body)).collectResponse {
                    onSuccess = { result, _, _ ->
                        Logger.d(TAG, msg = "图片上传成功")
                        Logger.d(TAG, msg = "返回URL: ${result?.data?.url}")
                        uploadImageResultLiveData.postValue(result)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "图片上传失败 - 返回数据为空")
                        uploadImageResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "图片上传失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                        uploadImageResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "图片上传异常: ${e.message}")
                uploadImageResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 提交扫视能力检测结果
     * @param patientId 患者ID
     * @param targetPoints 目标点序列
     * @param gazePoints 视线轨迹点列表
     * @param duration 测试持续时间(毫秒)
     * @param notes 测试备注
     * @param imageUrl 图片URL
     */
    fun submitSaccadeAbilityResult(
        patientId: Long,
        targetPoints: List<Pair<Float, Float>>,
        gazePoints: List<GazePoint>,
        duration: Int = 20000,
        notes: String = "扫视能力测试",
        imageUrl: String? = null
    ) {
        Logger.d(TAG, msg = "开始提交扫视能力检测结果")
        Logger.d(TAG, msg = "患者ID: $patientId, 目标点数量: ${targetPoints.size}, 轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "测试持续时间: ${duration}ms, 备注: $notes")
        Logger.d(TAG, msg = "图片URL: $imageUrl")
        
        viewModelScope.launch {
            try {
                // 构建提交参数
                val params = buildSubmitParams(
                    patientId, targetPoints, gazePoints, duration, notes, imageUrl
                )

                Logger.d(TAG, msg = "提交参数构建完成，开始发送请求")

                MutableStateFlow(saccadeAbilityRepository.submitSaccadeAbilityResult(params)).collectResponse {
                    onSuccess = { result, _, _ ->
                        Logger.d(TAG, msg = "扫视能力检测结果提交成功")
                        Logger.d(TAG, msg = "返回记录ID: ${result?.data}")
                        submitResultLiveData.postValue(result)
                    }
                    onDataEmpty = { _, _ ->
                        Logger.e(TAG, msg = "扫视能力检测结果提交失败 - 返回数据为空")
                        submitResultLiveData.postValue(null)
                    }
                    onFailed = { errorCode, errorMsg ->
                        Logger.e(TAG, msg = "扫视能力检测结果提交失败 - errorCode: $errorCode, errorMsg: $errorMsg")
                        submitResultLiveData.postValue(null)
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "扫视能力检测结果提交异常: ${e.message}")
                submitResultLiveData.postValue(null)
            }
        }
    }

    /**
     * 构建提交参数
     */
    private fun buildSubmitParams(
        patientId: Long,
        targetPoints: List<Pair<Float, Float>>,
        gazePoints: List<GazePoint>,
        duration: Int,
        notes: String,
        imageUrl: String?
    ): HashMap<String, Any> {
        val currentTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault()).format(Date())

        // 构建视线轨迹数据（保持原有简单结构）
        val gazeTrajectory = gazePoints.mapIndexed { index, point ->
            hashMapOf<String, Any>(
                "index" to (index + 1),
                "x" to (point.x ?: 0.5f),
                "y" to (point.y ?: 0.5f),
                "distance" to (point.dist ?: 50.0f),
                "duration" to (point.duration ?: 100)
            )
        }

        // 构建增强的扫视能力轨迹数据（包含扫视能力分析信息）
        val enhancedSaccadeTrajectory = buildEnhancedSaccadeTrajectory(targetPoints, gazePoints)

        // 构建扫视能力数据
        val saccadeAbilityData = hashMapOf<String, Any>(
            "targetPoints" to gson.toJson(targetPoints.map { listOf(it.first, it.second) }),
            "gazeTrajectoryJson" to gson.toJson(enhancedSaccadeTrajectory), // 使用增强版数据
            "totalSaccades" to targetPoints.size,
            "successfulSaccades" to calculateSuccessfulSaccades(targetPoints, gazePoints),
            "accuracyRate" to calculateAccuracyRate(targetPoints, gazePoints),
            "averageSaccadeTime" to calculateAverageSaccadeTime(gazePoints),
            "saccadeVelocity" to calculateSaccadeVelocity(gazePoints),
            "errorDistance" to calculateErrorDistance(targetPoints, gazePoints),
            "latency" to calculateLatency(gazePoints),
            "peakVelocity" to calculatePeakVelocity(gazePoints),
            "undershootRate" to 15.5,
            "overshootRate" to 12.2,
            "fixationStability" to 88.5
        )

        // 构建测试信息
        val testInfo = hashMapOf<String, Any>(
            "testType" to "SACCADE_ABILITY",
            "testSequence" to "03",
            "testDate" to currentTime,
            "duration" to duration,
            "calibrationParams" to "{\"param\": 1}",
            "environmentInfo" to "{\"description\": \"室内光线充足\"}",
            "notes" to notes
        )

        // 如果有图片URL，添加到testInfo中
        imageUrl?.let {
            testInfo["image_url"] = it
        }

        // 构建结果数据
        val resultData = hashMapOf<String, Any>(
            "gazeTrajectory" to gazeTrajectory,
            "saccadeAbilityData" to saccadeAbilityData
        )

        // 构建最终参数
        val params = hashMapOf<String, Any>(
            "patientId" to patientId,
            "testInfo" to testInfo,
            "resultData" to resultData
        )

        // 打印详细的参数日志
        Logger.d(TAG, msg = "=== 扫视能力检测数据上报参数详情 ===")
        Logger.d(TAG, msg = "患者ID: $patientId")
        Logger.d(TAG, msg = "测试类型: SACCADE_ABILITY")
        Logger.d(TAG, msg = "测试序号: 03")
        Logger.d(TAG, msg = "测试时间: $currentTime")
        Logger.d(TAG, msg = "测试持续时间: ${duration}ms")
        Logger.d(TAG, msg = "测试备注: $notes")
        Logger.d(TAG, msg = "图片URL: $imageUrl")
        Logger.d(TAG, msg = "目标点数量: ${targetPoints.size}")
        Logger.d(TAG, msg = "视线轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "有效视线点数量: ${gazePoints.count { it.checkValid() }}")

        // 打印目标点详情
        targetPoints.forEachIndexed { index, point ->
            Logger.d(TAG, msg = "目标点[$index]: (${point.first}, ${point.second})")
        }

        // 打印扫视能力分析数据
        Logger.d(TAG, msg = "=== 扫视能力分析结果 ===")
        Logger.d(TAG, msg = "总扫视次数: ${saccadeAbilityData["totalSaccades"]}")
        Logger.d(TAG, msg = "成功扫视次数: ${saccadeAbilityData["successfulSaccades"]}")
        Logger.d(TAG, msg = "准确率: ${saccadeAbilityData["accuracyRate"]}%")
        Logger.d(TAG, msg = "平均扫视时间(反应时间): ${saccadeAbilityData["averageSaccadeTime"]}ms")
        Logger.d(TAG, msg = "扫视速度: ${saccadeAbilityData["saccadeVelocity"]}度/秒")
        Logger.d(TAG, msg = "误差距离: ${saccadeAbilityData["errorDistance"]}像素")
        Logger.d(TAG, msg = "延迟: ${saccadeAbilityData["latency"]}ms")
        Logger.d(TAG, msg = "峰值速度: ${saccadeAbilityData["peakVelocity"]}度/秒")

        // 打印反应时间详细信息
        Logger.d(TAG, msg = "=== 反应时间详细分析 ===")
        val enhancedTrajectory = buildEnhancedSaccadeTrajectory(targetPoints, gazePoints)
        enhancedTrajectory.forEachIndexed { index, point ->
            val reactionTime = point["reactionTime"] as Int
            val isOnTarget = point["isOnTarget"] as Boolean
            val targetIndex = point["targetIndex"] as Int
            Logger.d(TAG, msg = "视线点[$index]: 反应时间=${reactionTime}ms, 命中目标=$isOnTarget, 目标索引=$targetIndex")
        }

        Logger.d(TAG, msg = "完整JSON参数: ${gson.toJson(params)}")
        Logger.d(TAG, msg = "=== 参数详情结束 ===")

        return params
    }

    /**
     * 构建增强的扫视能力轨迹数据
     */
    private fun buildEnhancedSaccadeTrajectory(
        targetPoints: List<Pair<Float, Float>>,
        gazePoints: List<GazePoint>
    ): List<HashMap<String, Any>> {
        return gazePoints.mapIndexed { index, point ->
            val gazeX = point.x ?: 0.5f
            val gazeY = point.y ?: 0.5f

            // 分析当前视线点的扫视能力相关信息
            val saccadeAnalysis = analyzeSaccadePoint(gazeX, gazeY, index, targetPoints, gazePoints)

            hashMapOf<String, Any>(
                "index" to (index + 1),
                "x" to gazeX,
                "y" to gazeY,
                "distance" to (point.dist ?: 50.0f),
                "duration" to (point.duration ?: 100),
                // 扫视能力分析字段
                "targetIndex" to saccadeAnalysis.targetIndex,
                "distanceToTarget" to saccadeAnalysis.distanceToTarget,
                "isOnTarget" to saccadeAnalysis.isOnTarget,
                "saccadeType" to saccadeAnalysis.saccadeType,
                "reactionTime" to saccadeAnalysis.reactionTime,
                "accuracy" to saccadeAnalysis.accuracy,
                "velocity" to saccadeAnalysis.velocity,
                "isValidSaccade" to saccadeAnalysis.isValidSaccade,
                "errorType" to saccadeAnalysis.errorType,
                "saccadeQuality" to saccadeAnalysis.saccadeQuality
            )
        }
    }

    /**
     * 分析单个视线点的扫视能力信息
     */
    private fun analyzeSaccadePoint(
        gazeX: Float,
        gazeY: Float,
        pointIndex: Int,
        targetPoints: List<Pair<Float, Float>>,
        gazePoints: List<GazePoint>
    ): SaccadeAnalysisResult {
        // 找到最近的目标点
        val nearestTarget = findNearestTarget(gazeX, gazeY, targetPoints)
        val targetIndex = nearestTarget.first
        val distanceToTarget = nearestTarget.second

        // 判断是否命中目标（阈值可调整）
        val targetThreshold = 0.1f // 10%的屏幕距离作为命中阈值
        val isOnTarget = distanceToTarget <= targetThreshold

        // 分析扫视类型
        val saccadeType = determineSaccadeType(pointIndex, gazePoints, targetPoints)

        // 计算反应时间（基于目标点和视线轨迹的近似计算）
        val reactionTime = calculatePointReactionTime(pointIndex, gazePoints, targetPoints)

        // 添加调试日志
        Logger.d(TAG, msg = "点[$pointIndex] 反应时间计算: ${reactionTime}ms, 目标点索引: $targetIndex, 距离: $distanceToTarget")

        // 计算精度评分（0-100）
        val accuracy = calculatePointAccuracy(distanceToTarget, targetThreshold)

        // 计算速度
        val velocity = calculatePointVelocity(pointIndex, gazePoints)

        // 判断是否为有效扫视
        val isValidSaccade = isOnTarget && reactionTime > 0 && velocity > 0

        // 确定错误类型
        val errorType = determineErrorType(distanceToTarget, targetThreshold, velocity)

        // 评估扫视质量
        val saccadeQuality = evaluateSaccadeQuality(accuracy, reactionTime, velocity, isOnTarget)

        return SaccadeAnalysisResult(
            targetIndex = targetIndex,
            distanceToTarget = distanceToTarget,
            isOnTarget = isOnTarget,
            saccadeType = saccadeType,
            reactionTime = reactionTime,
            accuracy = accuracy,
            velocity = velocity,
            isValidSaccade = isValidSaccade,
            errorType = errorType,
            saccadeQuality = saccadeQuality
        )
    }

    /**
     * 扫视分析结果数据类
     */
    private data class SaccadeAnalysisResult(
        val targetIndex: Int,           // 最近目标点索引
        val distanceToTarget: Float,    // 到目标点的距离
        val isOnTarget: Boolean,        // 是否命中目标
        val saccadeType: String,        // 扫视类型
        val reactionTime: Int,          // 反应时间(ms)
        val accuracy: Float,            // 精度评分(0-100)
        val velocity: Float,            // 扫视速度
        val isValidSaccade: Boolean,    // 是否为有效扫视
        val errorType: String,          // 错误类型
        val saccadeQuality: String      // 扫视质量评级
    )

    /**
     * 计算成功扫视次数
     */
    private fun calculateSuccessfulSaccades(targetPoints: List<Pair<Float, Float>>, gazePoints: List<GazePoint>): Int {
        // 基于增强分析重新计算成功扫视次数
        val enhancedTrajectory = buildEnhancedSaccadeTrajectory(targetPoints, gazePoints)
        return enhancedTrajectory.count { it["isValidSaccade"] as Boolean }
    }

    /**
     * 计算准确率
     */
    private fun calculateAccuracyRate(targetPoints: List<Pair<Float, Float>>, gazePoints: List<GazePoint>): Double {
        if (targetPoints.isEmpty()) return 0.0
        val successfulSaccades = calculateSuccessfulSaccades(targetPoints, gazePoints)
        return (successfulSaccades.toDouble() / targetPoints.size * 100).let { 
            String.format("%.1f", it).toDouble()
        }
    }

    /**
     * 计算平均扫视时间（基于真实的反应时间数据）
     */
    private fun calculateAverageSaccadeTime(gazePoints: List<GazePoint>): Double {
        if (gazePoints.isEmpty()) return 0.0

        // 尝试从C++层获取的真实反应时间数据计算平均扫视时间
        val validReactionTimes = mutableListOf<Int>()

        gazePoints.forEach { point ->
            if (point.checkValid()) {
                val duration = point.duration ?: 0
                // 检查是否为合理的反应时间范围（50ms-3000ms）
                if (duration in 50..3000) {
                    validReactionTimes.add(duration)
                }
            }
        }

        return if (validReactionTimes.isNotEmpty()) {
            // 使用真实反应时间计算平均值
            val avgReactionTime = validReactionTimes.average()
            String.format("%.1f", avgReactionTime).toDouble()
        } else {
            // 兼容旧的计算方式
            val validPoints = gazePoints.filter { it.checkValid() }
            if (validPoints.isEmpty()) return 0.0

            val totalDuration = validPoints.sumOf { it.duration ?: 0 }
            (totalDuration.toDouble() / validPoints.size).let {
                String.format("%.1f", it).toDouble()
            }
        }
    }

    /**
     * 计算扫视速度
     */
    private fun calculateSaccadeVelocity(gazePoints: List<GazePoint>): Double {
        if (gazePoints.size < 2) return 0.0
        
        var totalVelocity = 0.0
        var count = 0
        
        for (i in 1 until gazePoints.size) {
            val prev = gazePoints[i - 1]
            val curr = gazePoints[i]
            
            if (prev.checkValid() && curr.checkValid()) {
                val distance = sqrt(
                    (curr.x!! - prev.x!!).pow(2) + (curr.y!! - prev.y!!).pow(2)
                )
                val timeDiff = 100L // 假设每个点间隔100ms
                if (timeDiff > 0) {
                    totalVelocity += distance / (timeDiff / 1000.0) // 转换为秒
                    count++
                }
            }
        }
        
        return if (count > 0) {
            (totalVelocity / count * 100).let { // 转换为度/秒的近似值
                String.format("%.1f", it).toDouble()
            }
        } else 0.0
    }

    /**
     * 计算误差距离
     */
    private fun calculateErrorDistance(targetPoints: List<Pair<Float, Float>>, gazePoints: List<GazePoint>): Double {
        // 简化实现：计算视线点的平均偏差
        if (gazePoints.isEmpty()) return 0.0
        
        val validPoints = gazePoints.filter { it.checkValid() }
        if (validPoints.isEmpty()) return 0.0
        
        // 假设误差距离为视线点间距离的平均值
        var totalError = 0.0
        var count = 0
        
        for (i in 1 until validPoints.size) {
            val prev = validPoints[i - 1]
            val curr = validPoints[i]
            val distance = sqrt(
                (curr.x!! - prev.x!!).pow(2) + (curr.y!! - prev.y!!).pow(2)
            )
            totalError += distance * 100 // 转换为像素近似值
            count++
        }
        
        return if (count > 0) {
            (totalError / count).let {
                String.format("%.1f", it).toDouble()
            }
        } else 0.0
    }

    /**
     * 计算延迟（基于真实的反应时间数据）
     */
    private fun calculateLatency(gazePoints: List<GazePoint>): Double {
        if (gazePoints.isEmpty()) return 0.0

        // 尝试从C++层获取的真实反应时间数据计算延迟
        // C++层的GazeGlance类在gaze_data_list中存储的Point3f的z值就是反应时间
        val validReactionTimes = mutableListOf<Int>()

        // 从视线轨迹中提取反应时间信息
        gazePoints.forEachIndexed { index, point ->
            if (point.checkValid()) {
                // 如果duration字段实际存储的是从C++层获取的反应时间
                // 可以通过检查duration的值范围来判断
                val duration = point.duration ?: 0
                if (duration in 50..3000) { // 合理的反应时间范围
                    validReactionTimes.add(duration)
                }
            }
        }

        return if (validReactionTimes.isNotEmpty()) {
            // 使用真实反应时间的平均值作为延迟
            val avgReactionTime = validReactionTimes.average()
            String.format("%.1f", avgReactionTime).toDouble()
        } else {
            // 兼容旧的计算方式
            val validPoints = gazePoints.filter { it.checkValid() }
            if (validPoints.isEmpty()) return 0.0

            val avgDuration = validPoints.sumOf { it.duration ?: 0 }.toDouble() / validPoints.size
            (avgDuration * 2).let { // 假设延迟是持续时间的2倍
                String.format("%.1f", it).toDouble()
            }
        }
    }

    /**
     * 计算峰值速度
     */
    private fun calculatePeakVelocity(gazePoints: List<GazePoint>): Double {
        val avgVelocity = calculateSaccadeVelocity(gazePoints)
        return (avgVelocity * 1.4).let { // 假设峰值速度是平均速度的1.4倍
            String.format("%.1f", it).toDouble()
        }
    }

    /**
     * 找到最近的目标点
     */
    private fun findNearestTarget(gazeX: Float, gazeY: Float, targetPoints: List<Pair<Float, Float>>): Pair<Int, Float> {
        if (targetPoints.isEmpty()) return Pair(-1, Float.MAX_VALUE)

        var nearestIndex = 0
        var minDistance = Float.MAX_VALUE

        targetPoints.forEachIndexed { index, target ->
            val distance = sqrt((gazeX - target.first).pow(2) + (gazeY - target.second).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }

        return Pair(nearestIndex, minDistance)
    }

    /**
     * 确定扫视类型
     */
    private fun determineSaccadeType(pointIndex: Int, gazePoints: List<GazePoint>, targetPoints: List<Pair<Float, Float>>): String {
        if (pointIndex == 0) return "INITIAL"
        if (pointIndex >= gazePoints.size - 1) return "FINAL"

        val currentPoint = gazePoints[pointIndex]
        val prevPoint = gazePoints[pointIndex - 1]

        if (currentPoint.x == null || currentPoint.y == null ||
            prevPoint.x == null || prevPoint.y == null) {
            return "INVALID"
        }

        val distance = sqrt((currentPoint.x!! - prevPoint.x!!).pow(2) + (currentPoint.y!! - prevPoint.y!!).pow(2))

        return when {
            distance > 0.3f -> "LARGE_SACCADE"    // 大幅扫视
            distance > 0.1f -> "NORMAL_SACCADE"   // 正常扫视
            distance > 0.05f -> "SMALL_SACCADE"   // 小幅扫视
            else -> "FIXATION"                    // 注视
        }
    }

    /**
     * 计算点的反应时间
     * 优先使用时间戳信息计算准确的反应时间，如果没有时间戳则使用近似方法
     */
    private fun calculatePointReactionTime(pointIndex: Int, gazePoints: List<GazePoint>, targetPoints: List<Pair<Float, Float>>): Int {
        if (pointIndex == 0 || targetPoints.isEmpty()) return 0

        val currentPoint = gazePoints[pointIndex]
        val currentX = currentPoint.x ?: return 0
        val currentY = currentPoint.y ?: return 0

        // 方法1：如果有时间戳信息，使用时间戳计算反应时间
        currentPoint.timestamp?.let { currentTimestamp ->
            // 找到最近的目标点
            val nearestTarget = findNearestTarget(currentX, currentY, targetPoints)
            val targetIndex = nearestTarget.first
            val distanceToTarget = nearestTarget.second

            // 判断是否命中目标（阈值可调整）
            val targetThreshold = 0.1f
            if (distanceToTarget <= targetThreshold) {
                // 估算目标点出现时间（基于测试开始时间和目标点索引）
                // 假设测试开始时间为第一个视线点的时间戳减去其duration
                val testStartTime = gazePoints.firstOrNull()?.let { firstPoint ->
                    (firstPoint.timestamp ?: 0) - (firstPoint.duration ?: 100)
                } ?: 0

                // 假设每个目标点显示间隔约为2-3秒
                val estimatedTargetAppearTime = testStartTime + targetIndex * 2500L
                val reactionTime = (currentTimestamp - estimatedTargetAppearTime).toInt()

                // 限制反应时间在合理范围内 (50ms - 3000ms)
                if (reactionTime in 50..3000) {
                    return reactionTime
                }
            }
        }

        // 方法2：使用duration的累积时间作为近似计算（兼容旧数据）
        val cumulativeTime = gazePoints.take(pointIndex + 1).sumOf { it.duration ?: 100 }
        val nearestTarget = findNearestTarget(currentX, currentY, targetPoints)
        val targetIndex = nearestTarget.first

        // 基于目标点索引和累积时间计算反应时间
        val estimatedTargetAppearTime = targetIndex * 2500 // 2.5秒间隔
        val reactionTime = maxOf(0, cumulativeTime - estimatedTargetAppearTime)

        // 限制反应时间在合理范围内 (50ms - 2000ms)
        return reactionTime.coerceIn(50, 2000)
    }

    /**
     * 计算点的精度评分
     */
    private fun calculatePointAccuracy(distanceToTarget: Float, threshold: Float): Float {
        return if (distanceToTarget <= threshold) {
            100f - (distanceToTarget / threshold * 50f) // 命中目标时的精度评分
        } else {
            maxOf(0f, 50f - (distanceToTarget - threshold) * 100f) // 未命中时的精度评分
        }
    }

    /**
     * 计算点的速度
     */
    private fun calculatePointVelocity(pointIndex: Int, gazePoints: List<GazePoint>): Float {
        if (pointIndex == 0) return 0f

        val currentPoint = gazePoints[pointIndex]
        val prevPoint = gazePoints[pointIndex - 1]

        if (currentPoint.x == null || currentPoint.y == null ||
            prevPoint.x == null || prevPoint.y == null) {
            return 0f
        }

        val distance = sqrt((currentPoint.x!! - prevPoint.x!!).pow(2) + (currentPoint.y!! - prevPoint.y!!).pow(2))
        val timeDiff = 100f // 假设每个点间隔100ms

        return if (timeDiff > 0) distance / (timeDiff / 1000f) else 0f
    }

    /**
     * 确定错误类型
     */
    private fun determineErrorType(distanceToTarget: Float, threshold: Float, velocity: Float): String {
        return when {
            distanceToTarget <= threshold -> "NO_ERROR"
            distanceToTarget > threshold * 3 -> "LARGE_ERROR"
            velocity < 0.1f -> "SLOW_RESPONSE"
            velocity > 2.0f -> "OVERSHOOT"
            else -> "MINOR_ERROR"
        }
    }

    /**
     * 评估扫视质量
     */
    private fun evaluateSaccadeQuality(accuracy: Float, reactionTime: Int, velocity: Float, isOnTarget: Boolean): String {
        val score = when {
            isOnTarget && accuracy >= 80f && reactionTime <= 200 && velocity in 0.5f..1.5f -> 90f
            isOnTarget && accuracy >= 60f && reactionTime <= 300 -> 75f
            isOnTarget && accuracy >= 40f -> 60f
            accuracy >= 30f && reactionTime <= 500 -> 45f
            else -> 20f
        }

        return when {
            score >= 80f -> "EXCELLENT"
            score >= 60f -> "GOOD"
            score >= 40f -> "FAIR"
            else -> "POOR"
        }
    }
}
