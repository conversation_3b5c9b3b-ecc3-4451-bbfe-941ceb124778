#com.mitdd.gazetracker.GTApplication&com.mitdd.gazetracker.LauncherActivity&com.airdoc.videobox.MultiClickListener&com.mitdd.gazetracker.ai.AdaPreference(com.mitdd.gazetracker.ai.ChatWebActivity&com.mitdd.gazetracker.ai.bean.AuthCode1com.mitdd.gazetracker.ai.repository.AdaRepository(com.mitdd.gazetracker.ai.vm.AdaViewModel2com.mitdd.gazetracker.anim.CubicBezierInterpolator4com.mitdd.gazetracker.anim.CubicBezierPointEvaluator)com.mitdd.gazetracker.base.GTBaseActivity'com.mitdd.gazetracker.boot.BootReceiver3com.mitdd.gazetracker.boot.BootStartGTServiceWorker-com.mitdd.gazetracker.boot.GlobalBootReceiver-com.mitdd.gazetracker.common.CommonPreference<com.mitdd.gazetracker.common.decoration.CenterItemDecoration7com.mitdd.gazetracker.common.dialog.CommonLoadingDialog6com.mitdd.gazetracker.common.dialog.NotificationDialog3com.mitdd.gazetracker.common.dialog.task.DialogTask:com.mitdd.gazetracker.common.dialog.task.DialogTaskManager?com.mitdd.gazetracker.common.dialog.task.NotificationDialogTask1com.mitdd.gazetracker.common.widget.CommonAppView3com.mitdd.gazetracker.common.widget.CommonEmptyView7com.mitdd.gazetracker.common.widget.CommonExceptionView5com.mitdd.gazetracker.common.widget.CommonLoadingView5com.mitdd.gazetracker.common.widget.SeparatedEditText>com.mitdd.gazetracker.common.widget.SeparatedEditText.StyleDef=com.mitdd.gazetracker.common.widget.SeparatedEditText.TypeDef+com.mitdd.gazetracker.config.ConfigActivity,com.mitdd.gazetracker.desktop.DesktopService4com.mitdd.gazetracker.device.DeviceExceptionActivity0com.mitdd.gazetracker.device.DeviceExceptionMenu,com.mitdd.gazetracker.device.bean.DeviceInfo+com.mitdd.gazetracker.device.bean.IotConfig*com.mitdd.gazetracker.device.bean.Material.com.mitdd.gazetracker.device.bean.MaterialList6com.mitdd.gazetracker.device.enumeration.PlacementType8com.mitdd.gazetracker.device.repository.DeviceRepository/com.mitdd.gazetracker.device.vm.DeviceViewModel5com.mitdd.gazetracker.gaze.application.AppliedManager3com.mitdd.gazetracker.gaze.bean.CalibrateCoordinate1com.mitdd.gazetracker.gaze.bean.CalibrationResult(com.mitdd.gazetracker.gaze.bean.CureInfo)com.mitdd.gazetracker.gaze.bean.GazePoint/com.mitdd.gazetracker.gaze.bean.GazeTrackResult.com.mitdd.gazetracker.gaze.bean.GazeTrajectory2com.mitdd.gazetracker.gaze.bean.PosturalShiftParam8com.mitdd.gazetracker.gaze.bean.PostureCalibrationResult-com.mitdd.gazetracker.gaze.bean.ReadPointData-com.mitdd.gazetracker.gaze.bean.ReadTrackData+com.mitdd.gazetracker.gaze.bean.VisualPoint:com.mitdd.gazetracker.gaze.calibration.CalibrationActivityNcom.mitdd.gazetracker.gaze.calibration.CalibrationActivity.CalibrationWSClient?com.mitdd.gazetracker.gaze.calibration.CalibrationFailureDialog2com.mitdd.gazetracker.gaze.enumeration.AppliedMode6com.mitdd.gazetracker.gaze.enumeration.CalibrationMode3com.mitdd.gazetracker.gaze.enumeration.CoverChannel0com.mitdd.gazetracker.gaze.enumeration.CoverMode1com.mitdd.gazetracker.gaze.enumeration.CoverRange7com.mitdd.gazetracker.gaze.enumeration.PostureException2com.mitdd.gazetracker.gaze.enumeration.ServiceMode6com.mitdd.gazetracker.gaze.repository.ReportRepository1com.mitdd.gazetracker.gaze.track.GazeTrackService5com.mitdd.gazetracker.gaze.track.GazeWebSocketService0com.mitdd.gazetracker.gaze.track.TrackingManager6com.mitdd.gazetracker.gaze.upload.ReportRetrofitClient2com.mitdd.gazetracker.gaze.vm.CalibrationViewModel)com.mitdd.gazetracker.gaze.widget.DotView8com.mitdd.gazetracker.gaze.widget.PostureCalibrationView7com.mitdd.gazetracker.gaze.widget.TreatmentProgressView=com.mitdd.gazetracker.gaze.widget.TreatmentProgressView.State7com.mitdd.gazetracker.gaze.widget.VisualCalibrationView-com.mitdd.gazetracker.help.HelpCenterActivity,com.mitdd.gazetracker.help.HelpCenterAdapter7com.mitdd.gazetracker.help.HelpCenterAdapter.HelpHolder+com.mitdd.gazetracker.help.TutorialActivity2com.mitdd.gazetracker.medicalhome.HomeMainActivity2com.mitdd.gazetracker.medicalhome.HomeMainFragment.com.mitdd.gazetracker.medicalhome.TimeProgress0com.mitdd.gazetracker.medicalhome.bean.CommonApp7com.mitdd.gazetracker.medicalhome.bean.CurrentTreatment/com.mitdd.gazetracker.medicalhome.bean.FlipBeat9com.mitdd.gazetracker.medicalhome.bean.MedicalHomeProfile-com.mitdd.gazetracker.medicalhome.bean.Module7com.mitdd.gazetracker.medicalhome.bean.OcclusionTherapy,com.mitdd.gazetracker.medicalhome.bean.Train4com.mitdd.gazetracker.medicalhome.bean.TrainCategory2com.mitdd.gazetracker.medicalhome.bean.TrainConfig4com.mitdd.gazetracker.medicalhome.bean.TreatmentInfo1com.mitdd.gazetracker.medicalhome.bean.Treatments4com.mitdd.gazetracker.medicalhome.bean.VisionTherapy:com.mitdd.gazetracker.medicalhome.dialog.ConnectFlipDialog?com.mitdd.gazetracker.medicalhome.dialog.MaskTherapyStateDialog;com.mitdd.gazetracker.medicalhome.dialog.ReviewRemindDialog7com.mitdd.gazetracker.medicalhome.dialog.TrainEndDialog>com.mitdd.gazetracker.medicalhome.dialog.TrainSuggestionDialogBcom.mitdd.gazetracker.medicalhome.dialog.TreatmentActivationDialogHcom.mitdd.gazetracker.medicalhome.dialog.TreatmentActivationRemindDialogIcom.mitdd.gazetracker.medicalhome.dialog.TreatmentActivationSuccessDialogBcom.mitdd.gazetracker.medicalhome.dialog.TreatmentExpirationDialogHcom.mitdd.gazetracker.medicalhome.dialog.TreatmentExpirationRemindDialog=com.mitdd.gazetracker.medicalhome.dialog.TreatmentPauseDialog><EMAIL>:com.mitdd.gazetracker.medicalhome.enumeration.AmblyopicEye;com.mitdd.gazetracker.medicalhome.enumeration.CommonAppType;com.mitdd.gazetracker.medicalhome.enumeration.FlipBeatState7com.mitdd.gazetracker.medicalhome.enumeration.LimitType=com.mitdd.gazetracker.medicalhome.enumeration.TreatmentStatus7com.mitdd.gazetracker.medicalhome.mask.CommonAppAdapterGcom.mitdd.gazetracker.medicalhome.mask.CommonAppAdapter.CommonAppHolder8com.mitdd.gazetracker.medicalhome.mask.InstallAppAdapterIcom.mitdd.gazetracker.medicalhome.mask.InstallAppAdapter.InstallAppHolder5com.mitdd.gazetracker.medicalhome.mask.MaskPreference:com.mitdd.gazetracker.medicalhome.mask.MaskTherapyFragment>com.mitdd.gazetracker.medicalhome.mask.SelectCommonAppActivity6com.mitdd.gazetracker.medicalhome.menu.MenuPopupWindow:com.mitdd.gazetracker.medicalhome.preview.ParamPreviewView>com.mitdd.gazetracker.medicalhome.preview.ParamSettingActivityBcom.mitdd.gazetracker.medicalhome.receiver.RefreshBindUserReceiver;com.mitdd.gazetracker.medicalhome.repository.HomeRepository;com.mitdd.gazetracker.medicalhome.repository.MaskRepository<<EMAIL><com.mitdd.gazetracker.medicalhome.train.AITrainGuideActivity:com.mitdd.gazetracker.medicalhome.train.ConnectFlipAdapterEcom.mitdd.gazetracker.medicalhome.train.ConnectFlipAdapter.FlipHolder;com.mitdd.gazetracker.medicalhome.train.SelectTrainActivity:<EMAIL>;com.mitdd.gazetracker.medicalhome.train.VisualTrainFragmentAcom.mitdd.gazetracker.medicalhome.train.VisualTrainNoOpenFragmentAcom.mitdd.gazetracker.medicalhome.train.VisualTrainUnBindFragmentGcom.mitdd.gazetracker.medicalhome.treatment.TreatmentManagementActivityFcom.mitdd.gazetracker.medicalhome.treatment.TreatmentManagementAdapterVcom.mitdd.gazetracker.medicalhome.treatment.TreatmentManagementAdapter.TreatmentHolder;com.mitdd.gazetracker.medicalhome.treatment.TreatmentModuleBcom.mitdd.gazetracker.medicalhome.treatment.TreatmentModuleAdapterXcom.mitdd.gazetracker.medicalhome.treatment.TreatmentModuleAdapter.TreatmentModuleHolderIcom.mitdd.gazetracker.medicalhome.treatment.TreatmentModuleItemDecoration2com.mitdd.gazetracker.medicalhome.vm.HomeViewModel2com.mitdd.gazetracker.medicalhome.vm.MaskViewModel3com.mitdd.gazetracker.medicalhome.vm.TrainViewModel7com.mitdd.gazetracker.medicalhome.vm.TreatmentViewModel:com.mitdd.gazetracker.medicalhospital.HospitalInitFragment:com.mitdd.gazetracker.medicalhospital.HospitalMainActivity:com.mitdd.gazetracker.medicalhospital.HospitalMainFragment;com.mitdd.gazetracker.medicalhospital.HospitalModuleAdapterPcom.mitdd.gazetracker.medicalhospital.HospitalModuleAdapter.HospitalModuleHolderBcom.mitdd.gazetracker.medicalhospital.HospitalModuleItemDecorationAcom.mitdd.gazetracker.medicalhospital.HospitalSettingsPopupWindow8com.mitdd.gazetracker.medicalhospital.bean.MHospitalMode;com.mitdd.gazetracker.medicalhospital.bean.MHospitalProfile2com.mitdd.gazetracker.medicalhospital.bean.Patient5com.mitdd.gazetracker.medicalhospital.bean.PatientAdd6com.mitdd.gazetracker.medicalhospital.bean.PatientList?com.mitdd.gazetracker.medicalhospital.bean.PatientTrainDataList;com.mitdd.gazetracker.medicalhospital.bean.PatientTrainDataCcom.mitdd.gazetracker.medicalhospital.enumeration.HospitalModuleKeyIcom.mitdd.gazetracker.medicalhospital.inspection.InspectionCenterActivityHcom.mitdd.gazetracker.medicalhospital.inspection.InspectionCenterWebView=com.mitdd.gazetracker.medicalhospital.mt.MHMTTrainDataAdapterMcom.mitdd.gazetracker.medicalhospital.mt.MHMTTrainDataAdapter.TrainDataHolder>com.mitdd.gazetracker.medicalhospital.mt.MHMTTrainDataFragment<com.mitdd.gazetracker.medicalhospital.mt.MHospitalMTActivity<com.mitdd.gazetracker.medicalhospital.mt.MHospitalMTFragment?com.mitdd.gazetracker.medicalhospital.mt.MHospitalMTStateDialog6com.mitdd.gazetracker.medicalhospital.mt.NewUserDialog7com.mitdd.gazetracker.medicalhospital.mt.PatientAdapterEcom.mitdd.gazetracker.medicalhospital.mt.PatientAdapter.PatientHolder>com.mitdd.gazetracker.medicalhospital.mt.PatientItemDecoration?com.mitdd.gazetracker.medicalhospital.mt.PatientLibraryFragment;com.mitdd.gazetracker.medicalhospital.mt.SelectionAgeDialog=com.mitdd.gazetracker.medicalhospital.preference.MHPreferenceCcom.mitdd.gazetracker.medicalhospital.repository.HospitalRepositoryBcom.mitdd.gazetracker.medicalhospital.repository.PatientRepository?com.mitdd.gazetracker.medicalhospital.train.TrainCenterActivity>com.mitdd.gazetracker.medicalhospital.train.TrainCenterWebViewWcom.mitdd.gazetracker.medicalhospital.train.TrainCenterWebView.TrainCenterWebViewClient:com.mitdd.gazetracker.medicalhospital.vm.HospitalViewModel=com.mitdd.gazetracker.medicalhospital.vm.MHospitalMTViewModel9com.mitdd.gazetracker.medicalhospital.vm.PatientViewModel:<EMAIL><com.mitdd.gazetracker.movement.patient.EMPatientInfoActivity<com.mitdd.gazetracker.movement.patient.EMPatientInfoFragment?com.mitdd.gazetracker.movement.patient.EMPatientLibraryFragment=com.mitdd.gazetracker.movement.repository.EMPatientRepository.com.mitdd.gazetracker.movement.roi.GlideEngine7com.mitdd.gazetracker.movement.roi.ROIDetectingActivity7com.mitdd.gazetracker.movement.roi.ROIDetectionActivity3com.mitdd.gazetracker.movement.roi.ROIDetectionMenu=com.mitdd.gazetracker.movement.roi.ROIDetectionResultActivity9com.mitdd.gazetracker.movement.roi.ROIDetectionResultView.com.mitdd.gazetracker.movement.roi.ROIPathViewEcom.mitdd.gazetracker.movement.saccade.SaccadeAbilityEvaluateActivityKcom.mitdd.gazetracker.movement.saccade.SaccadeAbilityEvaluateResultActivityGcom.mitdd.gazetracker.movement.saccade.SaccadeAbilityEvaluateResultViewGcom.mitdd.gazetracker.movement.saccade.SaccadeAbilityEvaluatingFragmentCcom.mitdd.gazetracker.movement.saccade.SaccadeAbilityEvaluatingViewDcom.mitdd.gazetracker.movement.saccade.SaccadeAbilityExplainFragment4com.mitdd.gazetracker.movement.vm.EMPatientViewModel8com.mitdd.gazetracker.mqtt.listener.IConnectNotifyHolder+com.mitdd.gazetracker.net.AdaRetrofitClient1com.mitdd.gazetracker.net.CommonParamsInterceptor,com.mitdd.gazetracker.net.MainRetrofitClient0com.mitdd.gazetracker.net.OverseasRetrofitClient'com.mitdd.gazetracker.read.ReadActivity7com.mitdd.gazetracker.read.ReadAssessmentReportActivity+com.mitdd.gazetracker.read.ReadInitActivity4com.mitdd.gazetracker.read.ReadInitBasicInfoFragment6com.mitdd.gazetracker.read.ReadInitCalibrationFragment8com.mitdd.gazetracker.read.ReadInitStartEvaluateFragment+com.mitdd.gazetracker.read.ReadMainActivity5com.mitdd.gazetracker.read.ReadResultAnalysisActivity,com.mitdd.gazetracker.read.ReadTrackActivity(com.mitdd.gazetracker.read.ReadTrackView1com.mitdd.gazetracker.read.ReadTrackView.DrawMode*com.mitdd.gazetracker.read.bean.ReadResult0com.mitdd.gazetracker.read.enumeration.ReadGrade3com.mitdd.gazetracker.read.enumeration.ReadIdentity4com.mitdd.gazetracker.read.home.ReadHomeInitFragment4com.mitdd.gazetracker.read.home.ReadHomeMainActivity4com.mitdd.gazetracker.read.home.ReadHomeMainFragment,com.mitdd.gazetracker.read.home.ReadHomeMenu=com.mitdd.gazetracker.read.home.adapter.ReadHomeModuleAdapterRcom.mitdd.gazetracker.read.home.adapter.ReadHomeModuleAdapter.ReadHomeModuleHolder6com.mitdd.gazetracker.read.home.bean.MyopiaControlInfo4com.mitdd.gazetracker.read.home.bean.MyopiaTrainInfo4com.mitdd.gazetracker.read.home.bean.ReadHomeProfile1com.mitdd.gazetracker.read.home.bean.ReadHomeMode?com.mitdd.gazetracker.read.home.dialog.MyopiaControlStateDialog<<EMAIL>=com.mitdd.gazetracker.read.home.repository.ReadHomeRepositoryEcom.mitdd.gazetracker.read.home.train.MyopiaTrainCategoryListFragment9com.mitdd.gazetracker.read.home.train.MyopiaTrainFragment=com.mitdd.gazetracker.read.home.train.MyopiaTrainListFragment9com.mitdd.gazetracker.read.home.vm.MyopiaControlViewModel7com.mitdd.gazetracker.read.home.vm.MyopiaTrainViewModel4com.mitdd.gazetracker.read.home.vm.ReadHomeViewModel)com.mitdd.gazetracker.tsc.TscMainActivity)com.mitdd.gazetracker.tsc.bean.TscProfile2com.mitdd.gazetracker.tsc.repository.TscRepository)com.mitdd.gazetracker.tsc.vm.TscViewModel+com.mitdd.gazetracker.update.UpdateActivity)com.mitdd.gazetracker.update.UpdateDialog/com.mitdd.gazetracker.update.bean.AppUpdateInfo,com.mitdd.gazetracker.update.bean.AppVersion8com.mitdd.gazetracker.update.repository.UpdateRepository/com.mitdd.gazetracker.update.vm.UpdateViewModel'com.mitdd.gazetracker.user.BindActivity,com.mitdd.gazetracker.user.ConfirmBindDialog.com.mitdd.gazetracker.user.ProtocolWebActivity)com.mitdd.gazetracker.user.UserPreference+com.mitdd.gazetracker.user.bean.AccountInfo&com.mitdd.gazetracker.user.bean.Gender*com.mitdd.gazetracker.user.bean.VerifyInfo4com.mitdd.gazetracker.user.repository.UserRepository+com.mitdd.gazetracker.user.vm.UserViewModel2com.mitdd.gazetracker.websocket.GTWebSocketService<com.mitdd.gazetracker.databinding.ActivityMHospitalMtBinding?com.mitdd.gazetracker.databinding.FragmentPatientLibraryBinding<com.mitdd.gazetracker.databinding.FragmentMHospitalMtBinding><EMAIL>+com.mitdd.gazetracker.gaze.bean.TargetPoint                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            