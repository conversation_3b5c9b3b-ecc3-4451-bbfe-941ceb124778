package com.mitdd.gazetracker.movement.saccade

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.graphics.PointF
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import com.airdoc.component.common.base.BaseCommonFragment
import com.airdoc.component.common.handler.LifecycleHandler
import com.airdoc.component.common.ktx.countdown
import com.airdoc.component.common.ktx.dp2px
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.utils.ScreenUtil
import com.airdoc.component.common.utils.TimeUtils
import com.google.gson.Gson
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.BuildConfig
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.GazeConstants
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.gaze.bean.GazeTrajectory
import com.mitdd.gazetracker.gaze.track.GazeTrackService
import com.mitdd.gazetracker.movement.bean.SaccadeAbilityEvaluateResult
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.random.Random

/**
 * FileName: SaccadeAbilityEvaluatingFragment
 * Author by lilin,Date on 2024/12/11 14:12
 * PS: Not easy to write code, please indicate.
 * 扫视能力评估界面
 */
class SaccadeAbilityEvaluatingFragment : BaseCommonFragment() {

    companion object{
        private val TAG = SaccadeAbilityEvaluatingFragment::class.java.simpleName

        fun newInstance(): SaccadeAbilityEvaluatingFragment {
            return SaccadeAbilityEvaluatingFragment()
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_saccade_ability_evaluating
    }

    private val tvTime by id<TextView>(R.id.tv_time)
    private val evaluatingView by id<SaccadeAbilityEvaluatingView>(R.id.evaluating_view)
    private val clCountDown by id<ConstraintLayout>(R.id.cl_count_down)
    private val tvCountDown by id<TextView>(R.id.tv_count_down)

    private val mGson = Gson()

    private val handler = object : LifecycleHandler(Looper.getMainLooper(), this){
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            parseMessage(msg)
        }
    }
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Logger.d(TAG, msg = "onServiceConnected")
            if (service != null){
                mServiceMessage = Messenger(service)
                val message = Message.obtain().apply {
                    what = GazeConstants.MSG_SERVICE_CONNECTED
                    replyTo = mClientMessage
                }
                mServiceMessage?.send(message)
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Logger.d(TAG, msg = "onServiceDisconnected")
            mServiceMessage = null
        }
    }
    var mServiceMessage: Messenger? = null
    private var mClientMessage: Messenger = Messenger(handler)

    //生成随机点的范围
    private val rangeX = FloatArray(2)
    private val rangeY = FloatArray(2)
    //屏幕宽高
    private var screenWidth = 0
    private var screenHeight = 0

    private var mGazeTrajectory:GazeTrajectory? = null

    // 保存测试过程中生成的目标点序列
    private val targetPointsList = mutableListOf<Pair<Float, Float>>()

    override fun initParam() {
        super.initParam()
        screenWidth = ScreenUtil.getScreenWidth(mActivity)
        screenHeight = ScreenUtil.getScreenHeight(mActivity)
        rangeX.apply {
            this[0] = 100.dp2px(mActivity).toFloat() / screenWidth
            this[1] = (screenWidth - 100.dp2px(mActivity)).toFloat() / screenWidth
        }
        rangeY.apply {
            this[0] = 100.dp2px(mActivity).toFloat() / screenHeight
            this[1] = (screenHeight - 100.dp2px(mActivity)).toFloat() / screenHeight
        }
    }

    override fun initView() {
        super.initView()

        startPromptCountdown()
    }

    /**
     * 开始评估
     */
    private fun startEvaluating(){
        clCountDown.isVisible = false
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_ON_CAMERA
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_START_APPLIED_GLANCE
            }
        )
    }

    /**
     * 停止评估
     */
    private fun stopEvaluating(){
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_GET_GAZE_TRAJECTORY
        })
    }

    /**
     * 提示倒计时
     */
    private fun startPromptCountdown(){
        countdown(3000,1000,
            onTick = {
                tvCountDown.text = (it / 1000).toString()
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCompletion th = $th")
                if (th == null){
                    startEvaluating()
                }
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startPromptCountdown onCatch th = $th")
            }
        )
    }

    /**
     * 时间倒计时
     */
    private fun startTimeCountdown(){
        countdown(20 * 1000,1000,
            onTick = {
                if (it == 0L){
                    tvTime.text = "00:00"
                }else{
                    tvTime.text = TimeUtils.parseTimeToTimeString(it,"mm:ss")
                }
            },
            onCompletion = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCompletion th = $th")
                stopEvaluating()
            },
            onCatch = { th ->
                Logger.d(TAG, msg = "startTimeCountdown onCatch ${th.message}")
            }
        )
    }

    private var previousPoint: PointF? = null // 保存上一个生成的点
    /**
     * 生成随机点
     * @param minX x坐标最小值
     * @param maxX x坐标最大值
     * @param minY y坐标最小值
     * @param maxY y坐标最大值
     */
    private fun generateRandomPoint(minX:Float,maxX:Float,minY:Float,maxY:Float):PointF{
        // 随机生成一个新的点
        val x = Random.nextFloat() * (maxX - minX) + minX
        val y = Random.nextFloat() * (maxY - minY) + minY
        return PointF(x, y)
//        val minDistance = 200f // 最小距离要求
//        var newPoint: PointF
//        do {
//            // 随机生成一个新的点
//            val x = Random.nextFloat() * (maxX - minX) + minX
//            val y = Random.nextFloat() * (maxY - minY) + minY
//            newPoint = PointF(x, y)
//
//            // 如果是第一个点，直接返回
//            if (previousPoint == null) break
//
//            // 计算新点与上一个点的距离
//            val distance = sqrt(
//                (newPoint.x * screenWidth - previousPoint!!.x * screenWidth).pow(2) +
//                        (newPoint.y * screenHeight - previousPoint!!.y * screenHeight).pow(2)
//            )
//        } while (distance < minDistance) // 如果距离不足，重新生成
//
//        // 更新上一个点为当前生成的点
//        previousPoint = newPoint
//        return newPoint
    }

    /**
     * 设置扫视的目标点
     */
    private fun setGlancePoint(){
        val pointF = generateRandomPoint(rangeX[0], rangeX[1], rangeY[0], rangeY[1])

        Logger.d(TAG, msg = "生成新的扫视目标点: (${pointF.x}, ${pointF.y})")
        Logger.d(TAG, msg = "目标点屏幕坐标: (${pointF.x * ScreenUtil.getScreenWidth(mActivity)}, ${pointF.y * ScreenUtil.getScreenHeight(mActivity)})")

        // 保存目标点到序列中
        targetPointsList.add(Pair(pointF.x, pointF.y))
        Logger.d(TAG, msg = "目标点已保存到序列，当前序列长度: ${targetPointsList.size}")

        evaluatingView.startEvaluating(pointF)
        sendMessageToService(Message.obtain().apply {
            what = GazeConstants.MSG_SET_GLANCE_POINT
            data.putFloat(GazeConstants.KEY_X,pointF.x)
            data.putFloat(GazeConstants.KEY_Y,pointF.y)
        })

        Logger.d(TAG, msg = "目标点设置完成，等待用户扫视")
    }

    private fun parseMessage(msg: Message){
        when(msg.what){
            GazeConstants.MSG_APPLIED_GLANCE_STATE ->{
                val state = msg.data.getBoolean(GazeConstants.KEY_STATE)
                Logger.d(TAG, msg = "MSG_APPLIED_GLANCE_STATE state = $state")
                if (state){
                    startTimeCountdown()
                    setGlancePoint()
                }
            }
            GazeConstants.MSG_GAZE_TRAJECTORY_RESULT ->{
                lifecycleScope.launch {
                    val json = msg.data.getString(GazeConstants.KEY_GAZE_TRAJECTORY)
                    Logger.d(TAG, msg = "接收到扫视能力评估轨迹数据: $json")

                    mGazeTrajectory = try {
                        val trajectory = mGson.fromJson(json, GazeTrajectory::class.java)
                        Logger.d(TAG, msg = "轨迹数据解析成功，视线点数量: ${trajectory?.gaze?.size ?: 0}")

                        // 打印轨迹数据详情
                        trajectory?.gaze?.forEachIndexed { index, point ->
                            Logger.d(TAG, msg = "轨迹点[$index]: x=${point.x}, y=${point.y}, duration=${point.duration}ms, distance=${point.dist}")
                        }

                        trajectory
                    }catch (e:Exception){
                        Logger.e(TAG, msg = "轨迹数据解析失败: ${e.message}")
                        if (BuildConfig.DEBUG){
                            e.printStackTrace()
                        }
                        null
                    }
                    sendMessageToService(
                        Message.obtain().apply {
                            what = GazeConstants.MSG_STOP_APPLIED_GLANCE
                        },
                        Message.obtain().apply {
                            what = GazeConstants.MSG_STOP_TRACK
                        },
                        Message.obtain().apply {
                            what = GazeConstants.MSG_TURN_OFF_CAMERA
                        }
                    )

                    delay(200)

                    // 打印目标点序列详情
                    Logger.d(TAG, msg = "测试完成，目标点序列数量: ${targetPointsList.size}")
                    targetPointsList.forEachIndexed { index, point ->
                        Logger.d(TAG, msg = "目标点[$index]: x=${point.first}, y=${point.second}")
                    }

                    // 创建完整的评估结果数据
                    val evaluateResult = SaccadeAbilityEvaluateResult(
                        gazePoints = mGazeTrajectory?.gaze ?: emptyList(),
                        targetPoints = targetPointsList.toList()
                    )

                    LiveEventBus.get<SaccadeAbilityEvaluateResult>(SaccadeAbilityEvaluateResultActivity.INPUT_PARAM_EVALUATE_RESULT).post(evaluateResult)
                    startActivity(SaccadeAbilityEvaluateResultActivity.createIntent(mActivity))
                    mActivity.finish()
                }
            }
            GazeConstants.MSG_SACCADE_POINT_COMPLETE ->{
                Logger.d(TAG, msg = "当前扫视目标点完成，准备设置下一个目标点")
                setGlancePoint()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        mActivity.bindService(Intent(mActivity, GazeTrackService::class.java), serviceConnection, Context.BIND_AUTO_CREATE)
    }

    override fun onStop() {
        super.onStop()
        sendMessageToService(
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_APPLIED_GLANCE
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_STOP_TRACK
            },
            Message.obtain().apply {
                what = GazeConstants.MSG_TURN_OFF_CAMERA
            }
        )
        mActivity.unbindService(serviceConnection)
    }

    private fun sendMessageToService(vararg message: Message){
        message.forEach {
            mServiceMessage?.send(it)
        }
    }

}