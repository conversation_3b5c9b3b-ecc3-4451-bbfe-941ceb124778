package com.mitdd.gazetracker.movement.bean

import android.os.Parcelable
import com.mitdd.gazetracker.gaze.bean.GazePoint
import kotlinx.parcelize.Parcelize

/**
 * FileName: SaccadeAbilityEvaluateResult
 * Author by lilin,Date on 2025/6/21 15:00
 * PS: Not easy to write code, please indicate.
 * 扫视能力评估结果数据类
 */
@Parcelize
data class SaccadeAbilityEvaluateResult(
    // 视线轨迹点列表
    val gazePoints: List<GazePoint>,
    // 目标点序列（屏幕比例坐标）
    val targetPoints: List<Pair<Float, Float>>
) : Parcelable
