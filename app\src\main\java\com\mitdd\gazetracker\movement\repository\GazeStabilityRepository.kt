package com.mitdd.gazetracker.movement.repository

import com.airdoc.component.common.net.base.BaseRepository
import com.airdoc.component.common.net.entity.ApiResponse
import com.airdoc.component.common.log.Logger
import com.mitdd.gazetracker.movement.api.GazeStabilityApiService
import com.mitdd.gazetracker.movement.bean.GazeStabilityAdd
import com.mitdd.gazetracker.movement.bean.FileUploadResponse
import com.mitdd.gazetracker.net.MainRetrofitClient
import com.mitdd.gazetracker.net.UrlConfig
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody

class GazeStabilityRepository : BaseRepository() {

    /**
     * 上传图片
     * @param file 图片文件
     */
    suspend fun uploadImage(file: MultipartBody.Part): ApiResponse<FileUploadResponse> {
        return executeHttp {
            MainRetrofitClient.createService(GazeStabilityApiService::class.java, UrlConfig.MOVEMENT_DOMAIN)
                .uploadImage(file)
        }
    }

    /**
     * 提交注视稳定性检测结果
     * @param params 提交参数
     */
    suspend fun submitGazeStabilityResult(params: HashMap<String, Any>): ApiResponse<GazeStabilityAdd> {
        return executeHttp {
            val json = gson.toJson(params)
            val requestBody = json.toRequestBody("application/json;charset=UTF-8".toMediaTypeOrNull())
            MainRetrofitClient.createService(GazeStabilityApiService::class.java, UrlConfig.MOVEMENT_DOMAIN)
                .addGazeStability(requestBody)
        }
    }
}d