package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * FileName: TargetPoint
 * Author by AI Assistant, Date on 2025/6/21
 * PS: Not easy to write code, please indicate.
 * 扫视目标点（包含时间戳信息）
 */
@Parcelize
data class TargetPoint(
    //x坐标，这是一个比例值，根据屏幕宽度计算具体值(0..1)
    val x: Float,
    //y坐标，这是一个比例值，根据屏幕高度计算具体值(0..1)
    val y: Float,
    //目标点出现的时间戳，毫秒
    val appearanceTimestamp: Long,
    //目标点序号
    val index: Int,
    //是否已被命中
    var isHit: Boolean = false,
    //首次命中时间戳，毫秒
    var firstHitTimestamp: Long? = null
): Parcelable {

    /**
     * 计算反应时间（从目标点出现到首次命中的时间）
     * @return 反应时间（毫秒），如果未命中则返回null
     */
    fun getReactionTime(): Long? {
        return firstHitTimestamp?.let { it - appearanceTimestamp }
    }

    /**
     * 标记目标点被命中
     * @param hitTimestamp 命中时间戳
     */
    fun markAsHit(hitTimestamp: Long) {
        if (!isHit) {
            isHit = true
            firstHitTimestamp = hitTimestamp
        }
    }

    /**
     * 检查坐标是否有效
     * @return true 表示有效
     */
    fun isValid(): Boolean {
        return x in 0.0f..1.0f && y in 0.0f..1.0f
    }

    /**
     * 计算与指定坐标的距离
     * @param gazeX 视线X坐标
     * @param gazeY 视线Y坐标
     * @return 距离值
     */
    fun distanceTo(gazeX: Float, gazeY: Float): Float {
        val dx = x - gazeX
        val dy = y - gazeY
        return kotlin.math.sqrt(dx * dx + dy * dy)
    }
}
