package com.mitdd.gazetracker.gaze.bean

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize

/**
 * FileName: GazePoint
 * Author by lilin,Date on 2024/12/11 13:33
 * PS: Not easy to write code, please indicate.
 * 视线点
 */
@Parcelize
data class GazePoint(
    //x坐标，这是一个比例值，根据屏幕宽度计算具体值(0..1)
    var x: Float? = null,
    //y坐标，这是一个比例值，根据屏幕高度计算具体值(0..1)
    var y: Float? = null,
    //人眼离屏幕距离，正常数值在35~65之间。单位为cm
    var dist: Float? = null,
    //视线在该点持续时间，毫秒
    var duration:Int? = null,
    //序号
    var index:Int? = null,
    //时间戳，毫秒（用于计算反应时间）
    var timestamp: Long? = null
): Parcelable{

    /**
     * 检查参数是否有效
     * @return true 表示有效
     */
    fun checkValid():<PERSON>olean{
        return x?.let { it > 0 && it < 1 } == true && y?.let { it > 0 && it < 1 } == true
    }
}
