package com.mitdd.gazetracker.movement.vm

import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.gaze.bean.TargetPoint
import org.junit.Test
import org.junit.Assert.*

/**
 * 扫视能力反应时间计算测试
 */
class SaccadeReactionTimeTest {

    @Test
    fun testTargetPointReactionTime() {
        // 创建目标点
        val targetPoint = TargetPoint(
            x = 0.5f,
            y = 0.5f,
            appearanceTimestamp = 1000L,
            index = 0
        )

        // 测试未命中时的反应时间
        assertNull("未命中时应返回null", targetPoint.getReactionTime())

        // 测试命中后的反应时间
        targetPoint.markAsHit(1250L)
        assertEquals("反应时间应为250ms", 250L, targetPoint.getReactionTime())

        // 测试重复命中不会改变首次命中时间
        targetPoint.markAsHit(1500L)
        assertEquals("重复命中不应改变反应时间", 250L, targetPoint.getReactionTime())
    }

    @Test
    fun testTargetPointDistanceCalculation() {
        val targetPoint = TargetPoint(
            x = 0.5f,
            y = 0.5f,
            appearanceTimestamp = 1000L,
            index = 0
        )

        // 测试距离计算
        val distance1 = targetPoint.distanceTo(0.5f, 0.5f)
        assertEquals("相同位置距离应为0", 0.0f, distance1, 0.001f)

        val distance2 = targetPoint.distanceTo(0.6f, 0.5f)
        assertEquals("水平距离0.1的距离", 0.1f, distance2, 0.001f)

        val distance3 = targetPoint.distanceTo(0.5f, 0.6f)
        assertEquals("垂直距离0.1的距离", 0.1f, distance3, 0.001f)
    }

    @Test
    fun testGazePointWithTimestamp() {
        // 测试带时间戳的视线点
        val gazePoint = GazePoint(
            x = 0.5f,
            y = 0.5f,
            dist = 50.0f,
            duration = 250,
            index = 1,
            timestamp = 1250L
        )

        assertTrue("视线点应该有效", gazePoint.checkValid())
        assertEquals("时间戳应该正确", 1250L, gazePoint.timestamp)
    }

    @Test
    fun testReactionTimeValidation() {
        // 测试反应时间的合理性验证
        val validReactionTimes = listOf(50, 100, 250, 500, 800, 1200, 2000, 3000)
        val invalidReactionTimes = listOf(-10, 0, 30, 3500, 5000, 10000)

        validReactionTimes.forEach { time ->
            assertTrue("${time}ms应该是有效的反应时间", time in 50..3000)
        }

        invalidReactionTimes.forEach { time ->
            assertFalse("${time}ms应该是无效的反应时间", time in 50..3000)
        }
    }

    @Test
    fun testReactionTimeCalculationScenario() {
        // 模拟真实的扫视测试场景
        val testStartTime = 1000L
        val targetAppearInterval = 2500L

        // 目标点序列
        val targetPoints = listOf(
            Pair(0.2f, 0.2f), // 目标点0，出现时间: 1000ms
            Pair(0.8f, 0.2f), // 目标点1，出现时间: 3500ms
            Pair(0.8f, 0.8f), // 目标点2，出现时间: 6000ms
        )

        // 视线轨迹点（模拟用户响应）
        val gazePoints = listOf(
            GazePoint(0.1f, 0.1f, 50.0f, 100, 0, 1100L), // 100ms后开始移动
            GazePoint(0.15f, 0.15f, 50.0f, 100, 1, 1200L), // 200ms
            GazePoint(0.2f, 0.2f, 50.0f, 100, 2, 1300L),   // 300ms，命中目标点0
            GazePoint(0.5f, 0.3f, 50.0f, 100, 3, 3600L),   // 移向目标点1
            GazePoint(0.7f, 0.2f, 50.0f, 100, 4, 3700L),   // 
            GazePoint(0.8f, 0.2f, 50.0f, 100, 5, 3800L),   // 800ms后命中目标点1
        )

        // 验证反应时间计算
        // 目标点0的反应时间：1300 - 1000 = 300ms
        // 目标点1的反应时间：3800 - 3500 = 300ms

        val expectedReactionTimes = listOf(300L, 300L)
        
        // 这里可以添加实际的反应时间计算验证
        // 由于需要访问ViewModel的私有方法，这里只是展示测试思路
        
        assertTrue("测试场景设置正确", gazePoints.size == 6)
        assertTrue("目标点设置正确", targetPoints.size == 3)
    }
}
