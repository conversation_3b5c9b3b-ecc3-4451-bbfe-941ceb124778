package com.mitdd.gazetracker.movement.saccade

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import com.airdoc.component.common.ktx.id
import com.airdoc.component.common.ktx.isVisible
import com.airdoc.component.common.log.Logger
import com.jeremyliao.liveeventbus.LiveEventBus
import com.mitdd.gazetracker.R
import com.mitdd.gazetracker.gaze.bean.GazePoint
import com.mitdd.gazetracker.movement.EyeMovementResultActivity
import com.mitdd.gazetracker.movement.bean.SaccadeAbilityEvaluateResult
import com.mitdd.gazetracker.movement.patient.EMPatientManager
import com.mitdd.gazetracker.movement.vm.EMPatientViewModel
import com.mitdd.gazetracker.movement.vm.SaccadeAbilityViewModel
import java.io.File
import java.io.FileOutputStream

/**
 * FileName: SaccadeAbilityEvaluateResultActivity
 * Author by lilin,Date on 2024/12/11 14:52
 * PS: Not easy to write code, please indicate.
 * 追随能力评估结果
 */
class SaccadeAbilityEvaluateResultActivity : EyeMovementResultActivity() {

    companion object{
        private val TAG = SaccadeAbilityEvaluateResultActivity::class.java.simpleName

        const val INPUT_PARAM_EVALUATE_RESULT = "evaluate_result"

        fun createIntent(context: Context): Intent {
            return Intent(context, SaccadeAbilityEvaluateResultActivity::class.java)
        }
    }

    private val evaluateResultView by id<SaccadeAbilityEvaluateResultView>(R.id.evaluate_result_view)
    private val saccadeAbilityViewModel: SaccadeAbilityViewModel by viewModels()
    private val patientViewModel: EMPatientViewModel by viewModels()

    // 存储视线轨迹数据和目标点数据
    private var gazePoints: List<GazePoint> = emptyList()
    private var targetPoints: List<Pair<Float, Float>> = emptyList()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_saccade_ability_evaluate_result)

        initView()
        initObserver()
        initData()
        initViewModelObserver()
    }

    private fun initView(){
        getTitleView().text = getString(R.string.str_saccade_ability_evaluate_result)
        getEvaluateResultView().apply {
            text = getString(R.string.str_instability)
            setBackgroundResource(R.drawable.common_eb4e89_round_5_bg)
        }
        getPointNumberView().isVisible = true
        getAverageDurationView().isVisible = true
    }

    private fun initObserver(){
        LiveEventBus.get<SaccadeAbilityEvaluateResult>(INPUT_PARAM_EVALUATE_RESULT).observeSticky(this){ result ->
            if (result != null){
                Logger.d(TAG, msg = "接收到扫视能力评估结果数据")
                Logger.d(TAG, msg = "视线点数量: ${result.gazePoints.size}")
                Logger.d(TAG, msg = "目标点数量: ${result.targetPoints.size}")

                // 打印视线点数据详情
                result.gazePoints.forEachIndexed { index, point ->
                    Logger.d(TAG, msg = "视线点[$index]: x=${point.x}, y=${point.y}, duration=${point.duration}ms")
                }

                // 打印目标点数据详情
                result.targetPoints.forEachIndexed { index, point ->
                    Logger.d(TAG, msg = "目标点[$index]: x=${point.first}, y=${point.second}")
                }

                gazePoints = result.gazePoints
                targetPoints = result.targetPoints
                evaluateResultView.drawResult(result.gazePoints)

                // 等待视图绘制完成后自动开始上传流程
                evaluateResultView.post {
                    // 延迟一段时间确保绘制完成
                    evaluateResultView.postDelayed({
                        startDataUploadProcess()
                    }, 500)
                }
            }
        }
    }

    private fun initData() {
        Logger.d(TAG, msg = "初始化扫视能力评估结果页面数据")
    }

    private fun initViewModelObserver() {
        // 观察图片上传结果
        saccadeAbilityViewModel.uploadImageResultLiveData.observe(this, Observer { result ->
            if (result != null && result.data != null) {
                Logger.d(TAG, msg = "图片上传成功，URL: ${result.data.url}")
                // 图片上传成功后提交检测结果
                submitDataToServerWithImage(result.data.url)
            } else {
                Logger.e(TAG, msg = "图片上传失败")
                // 即使图片上传失败，也可以提交检测结果（不包含图片URL）
                submitDataToServerWithImage(null)
            }
        })

        // 观察检测结果提交结果
        saccadeAbilityViewModel.submitResultLiveData.observe(this, Observer { result ->
            if (result != null && result.data != null) {
                Logger.d(TAG, msg = "扫视能力检测结果提交成功，记录ID: ${result.data}")
                Toast.makeText(this, "数据提交成功", Toast.LENGTH_SHORT).show()
            } else {
                Logger.e(TAG, msg = "扫视能力检测结果提交失败")
                Toast.makeText(this, "数据提交失败", Toast.LENGTH_SHORT).show()
            }
        })
    }



    /**
     * 开始数据上传流程
     */
    private fun startDataUploadProcess() {
        Logger.d(TAG, msg = "开始扫视能力评估数据上传流程")

        // 检查是否有有效的视线轨迹数据
        if (gazePoints.isEmpty()) {
            Logger.e(TAG, msg = "视线轨迹数据为空，无法上传")
            Toast.makeText(this, "测试数据为空，无法上传", Toast.LENGTH_SHORT).show()
            return
        }

        // 保存并上传结果图片
        saveAndUploadResultImage()
    }

    /**
     * 保存结果图片并上传
     */
    private fun saveAndUploadResultImage() {
        try {
            Logger.d(TAG, msg = "开始保存扫视能力评估结果图片")

            // 获取视图的bitmap
            val bitmap = evaluateResultView.getViewBitmap()
            if (bitmap != null) {
                // 保存到文件
                val file = File(cacheDir, "saccade_ability_result_${System.currentTimeMillis()}.png")
                val fos = FileOutputStream(file)
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                fos.close()

                Logger.d(TAG, msg = "结果图片保存成功: ${file.absolutePath}")
                Logger.d(TAG, msg = "图片大小: ${file.length()} bytes")

                // 上传图片
                saccadeAbilityViewModel.uploadImage(file)
            } else {
                Logger.e(TAG, msg = "无法获取结果视图的bitmap")
                // 直接提交结果（不包含图片）
                submitDataToServerWithImage(null)
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "保存结果图片失败: ${e.message}")
            // 直接提交结果（不包含图片）
            submitDataToServerWithImage(null)
        }
    }

    /**
     * 提交数据到服务器（包含图片URL）
     */
    private fun submitDataToServerWithImage(imageUrl: String?) {
        val currentPatient = EMPatientManager.getEMPatient()
        if (currentPatient?.id == null) {
            Logger.e(TAG, msg = "当前患者信息为空，无法提交数据")
            Toast.makeText(this, "患者信息缺失，无法提交数据", Toast.LENGTH_SHORT).show()
            return
        }

        val patientId = try {
            currentPatient.id!!.toLong()
        } catch (e: NumberFormatException) {
            Toast.makeText(this, "患者ID格式错误", Toast.LENGTH_SHORT).show()
            return
        }

        if (gazePoints.isEmpty()) {
            Logger.e(TAG, msg = "视线轨迹数据为空，无法提交")
            Toast.makeText(this, "测试数据为空，无法提交", Toast.LENGTH_SHORT).show()
            return
        }

        Logger.d(TAG, msg = "开始提交扫视能力检测数据")
        Logger.d(TAG, msg = "患者ID: $patientId, 目标点数量: ${targetPoints.size}, 轨迹点数量: ${gazePoints.size}")
        Logger.d(TAG, msg = "图片URL: $imageUrl")

        saccadeAbilityViewModel.submitSaccadeAbilityResult(
            patientId = patientId,
            targetPoints = targetPoints,
            gazePoints = gazePoints,
            duration = 20000,
            notes = "扫视能力测试",
            imageUrl = imageUrl
        )
    }

    override fun getSerialNumberType():String {
        return "03"
    }

}